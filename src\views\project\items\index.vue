<template>
  <div class="go-project-items">
    <Project2TemplateProvider>
      <project-items-list></project-items-list>
    </Project2TemplateProvider>
  </div>
</template>

<script setup lang="ts">
import { ProjectItemsList } from './components/ProjectItemsList'
import Project2TemplateProvider from './components/Project2TemplateProvider/index.vue'
</script>

<style lang="scss" scoped>
@include go(project-items) {
  padding: 20px 20px;
}
</style>
