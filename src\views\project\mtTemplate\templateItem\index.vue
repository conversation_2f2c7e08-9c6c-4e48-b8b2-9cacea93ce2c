<template>
  <div v-if="cardData" class="go-template-item">
    <n-card hoverable size="small">
      <div class="list-content">
        <!-- 顶部按钮 -->
        <div class="list-content-top">
          <mac-os-control-btn
            class="top-btn"
            :hidden="['remove']"
            @close="emit('delete')"
            @resize="emit('resize')"
          ></mac-os-control-btn>
        </div>
        <!-- 中间 -->
        <div class="list-content-img" @click="resizeHandle">
          <n-image
            object-fit="contain"
            height="180"
            preview-disabled
            :src="cardData.indexImage"
            :alt="cardData.name"
            :fallback-src="requireErrorImg()"
          ></n-image>
        </div>
      </div>
      <template #action>
        <div class="go-flex-items-center list-footer" justify="space-between">
          <n-text class="go-ellipsis-1">
            {{ cardData.name || cardData.id || '未命名' }}
          </n-text>
          <!-- 工具 -->
          <div class="go-flex-items-center list-footer-ri">
            <n-space>
              <n-text>
                <n-badge class="go-animation-twinkle" dot :color="releaseColor"></n-badge>
                {{ releaseText }}
              </n-text>

              <template v-for="item in fnBtnList" :key="item.key">
                <template v-if="item.key === 'select'">
                  <n-dropdown
                    trigger="hover"
                    placement="bottom"
                    :options="selectOptions"
                    :show-arrow="true"
                    @select="handleSelect"
                  >
                    <n-button size="small">
                      <template #icon>
                        <component :is="item.icon"></component>
                      </template>
                    </n-button>
                  </n-dropdown>
                </template>

                <n-tooltip v-else placement="bottom" trigger="hover">
                  <template #trigger>
                    <n-button size="small" @click="handleSelect(item.key)">
                      <template #icon>
                        <component :is="item.icon"></component>
                      </template>
                    </n-button>
                  </template>
                  <component :is="item.label"></component>
                </n-tooltip>
              </template>
            </n-space>
            <!-- end -->
          </div>
        </div>
      </template>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { MacOsControlBtn } from '@/components/Tips/MacOsControlBtn'
import { icon } from '@/plugins'
import type { Template } from '@/template'
import { templateLangRender, templateLangT } from '@/template/i18n'
import { useMyTemplate } from '@/template/useMyTemplate'
import { renderIcon, renderLang, requireErrorImg } from '@/utils'
import type { PropType, VNode } from 'vue'
import { computed, reactive, ref, toValue } from 'vue'

const { unRelease, release, remove, usage } = useMyTemplate({
  immediate: false,
})
const {
  EllipsisHorizontalCircleSharpIcon,
  CopyIcon,
  TrashIcon,
  PencilIcon,
  DownloadIcon,
  BrowsersOutlineIcon,
  HammerIcon,
  SendIcon,
} = icon.ionicons5

const emit = defineEmits(['usage', 'release', 'unRelease', 'edit', 'delete', 'preview', 'resize', 'delete_success'])

const props = defineProps({
  cardData: Object as PropType<Template>,
})

const releaseText = computed(() => {
  return props.cardData?.release ? templateLangT('released') : templateLangT('unreleased')
})

const releaseColor = computed(() => {
  return props.cardData?.release ? '#34c749' : '#fcbc40'
})

const isRelease = computed(() => {
  return props.cardData?.release
})

interface ActionItem {
  label: string | (() => VNode)
  key: string
  icon: string | (() => VNode)
  onClick?: () => void
}

const fnBtnList = reactive<ActionItem[]>([
  {
    label: templateLangRender('usage'),
    key: 'usage',
    icon: renderIcon(CopyIcon),
    onClick: () => {
      usage(props.cardData!.id)
    },
  },
  {
    label: renderLang('global.r_more'),
    key: 'select',
    icon: renderIcon(EllipsisHorizontalCircleSharpIcon),
  },
])

const selectOptions = ref<ActionItem[]>([
  {
    label: templateLangRender('edit'),
    key: 'edit',
    icon: renderIcon(BrowsersOutlineIcon),
    onClick: () => {
      alert('TDOD: edit')
    },
  },
  {
    label: () => (toValue(isRelease) ? templateLangT('unRelease') : templateLangT('release')),
    key: 'release',
    icon: renderIcon(SendIcon),
    onClick: async () => {
      if (toValue(isRelease)) {
        await unRelease(props.cardData!.id)
        props.cardData!.release = false
      } else {
        await release(props.cardData!.id)
        props.cardData!.release = true
      }
    },
  },
  {
    label: renderLang('global.r_delete'),
    key: 'delete',
    icon: renderIcon(TrashIcon),
    onClick: async () => {
      await remove(props.cardData!.id)
      emit('delete_success')
    },
  },
])

const handleSelect = (key: string) => {
  const item = [...fnBtnList, ...selectOptions.value].find(item => item.key === key)
  if (item?.onClick) {
    item.onClick()
  }
}
</script>

<style lang="scss" scoped>
$contentHeight: 180px;
@include go('template-item') {
  position: relative;
  border-radius: $--border-radius-base;
  border: 1px solid rgba(0, 0, 0, 0);
  @extend .go-transition;
  &:hover {
    @include hover-border-color('hover-border-color');
  }
  .list-content {
    margin-top: 20px;
    margin-bottom: 5px;
    cursor: pointer;
    border-radius: $--border-radius-base;
    @include background-image('background-point');
    @extend .go-point-bg;
    &-top {
      position: absolute;
      top: 10px;
      left: 10px;
      height: 22px;
    }
    &-img {
      height: $contentHeight;
      @extend .go-flex-center;
      @extend .go-border-radius;
      @include deep() {
        img {
          @extend .go-border-radius;
        }
      }
    }
  }
  .list-footer {
    flex-wrap: nowrap;
    justify-content: space-between;
    line-height: 30px;
    &-ri {
      justify-content: flex-end;
      min-width: 180px;
    }
  }
}
</style>
