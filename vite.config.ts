import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { OUTPUT_DIR, brotliSize, chunkSizeWarningLimit, terserOptions, rollupOptions } from './build/constant'
import { compression } from 'vite-plugin-compression2'
import { axiosPre } from './src/settings/httpSetting'
import { viteMockServe } from 'vite-plugin-mock'
import monacoEditorPlugin from 'vite-plugin-monaco-editor'
import { fileURLToPath } from 'url'

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir)
}

export default defineConfig(({ mode }) => {
  return {
    base: process.env.NODE_ENV === 'production' ? './' : '/',
    // 路径重定向
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    // 全局 css 注册
    css: {
      preprocessorOptions: {
        scss: {
          javascriptEnabled: true,
          additionalData: `@import "src/styles/common/style.scss";`
        }
      }
    },
    // 开发服务器配置
    server: {
      host: true,
      open: false,
      proxy: {
        [axiosPre]: {
          // @ts-ignore
          target: loadEnv(mode, process.cwd()).VITE_DEV_PATH,
          changeOrigin: true,
          ws: true,
          secure: true
        }
      }
    },
    plugins: [
      vue({
        template: {
          compilerOptions: {
            // 排除 iconify 图标影子组件编译报错
            isCustomElement: tag => tag.startsWith('iconify-icon')
          }
        }
      }),
      monacoEditorPlugin.default({
        languageWorkers: ['editorWorkerService', 'typescript', 'json', 'html']
      }),
      viteMockServe({
        mockPath: '/src/api/mock',
        // 开发打包开关
        localEnabled: true,
        // 生产打包开关
        prodEnabled: true,
        // 打开后，可以读取 ts 文件模块。 请注意，打开后将无法监视.js 文件
        supportTs: true,
        // 监视文件更改
        watchFiles: true
      }),
      // 压缩
      compression({
        exclude: [/config.js/]
      })
    ],
    build: {
      target: 'chrome89',
      outDir: OUTPUT_DIR,
      // minify: 'terser', // 如果需要用terser混淆，可打开这两行
      // terserOptions: terserOptions,
      rollupOptions: rollupOptions,
      reportCompressedSize: brotliSize,
      chunkSizeWarningLimit: chunkSizeWarningLimit
    }
  }
})
