{"name": "go-view", "version": "2.2.8", "type": "module", "packageManager": "pnpm@8.15.9", "scripts": {"prepare": "husky", "dev": "vite --host", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "new": "plop --plopfile ./plop/plopfile.js", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write ."}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@iconify/json": "^2.2.158", "@vicons/carbon": "^0.12.0", "@vicons/ionicons5": "~0.11.0", "@vueuse/core": "^7.7.1", "animate.css": "^4.1.1", "axios": "^1.4.0", "color": "^4.2.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "dom-helpers": "^5.2.1", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "echarts-stat": "^1.2.0", "echarts-wordcloud": "^2.0.0", "gsap": "^3.11.3", "highlight.js": "^11.5.0", "html2canvas": "^1.4.1", "iconify-icon": "^1.0.8", "keymaster": "^1.6.2", "lodash": "~4.17.21", "mitt": "^3.0.0", "monaco-editor": "^0.33.0", "naive-ui": "2.34.3", "pinia": "^2.0.13", "screenfull": "^6.0.1", "three": "^0.145.0", "vue": "3.5.13", "vue-echarts": "^6.0.2", "vue-i18n": "9.2.2", "vue-router": "4.0.12", "vue3-lazyload": "^0.2.5-beta", "vue3-sketch-ruler": "^1.3.3", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node22": "22.0.0", "@types/color": "^3.0.3", "@types/crypto-js": "^4.1.1", "@types/keymaster": "^1.6.30", "@types/lodash": "^4.14.184", "@types/node": "22.9.0", "@types/three": "^0.144.0", "@vitejs/plugin-vue": "5.2.0", "@vitejs/plugin-vue-jsx": "4.1.0", "@vue/eslint-config-prettier": "10.1.0", "@vue/eslint-config-typescript": "14.1.3", "@vue/tsconfig": "0.6.0", "eslint": "9.15.0", "eslint-plugin-vue": "9.31.0", "husky": "9.1.7", "lint-staged": "15.2.10", "npm-run-all2": "7.0.1", "picocolors": "1.1.1", "plop": "4.0.1", "prettier": "3.3.3", "sass-embedded": "1.81.0", "typescript": "5.6.3", "vite": "5.4.11", "vite-plugin-compression2": "1.3.1", "vite-plugin-mock": "3.0.2", "vite-plugin-monaco-editor": "^1.1.0", "vue-tsc": "2.1.10"}}