import type { MyResponseType } from '@/api/axios'
import { formAxios } from '@/api/axios'
import { encodeBase64, httpErrorHandle } from '@/utils'
import type { DataSourceItem } from './form'

export const getDataSourceByPageApi = async (data: object) => {
  try {
    const res = await formAxios.post<unknown, MyResponseType<DataSourceItem[]>>(
      '/api/DataSource/GetDataSourceByPage',
      data,
    )
    return res
  } catch (err) {
    httpErrorHandle()
  }
}

export const getDataSourceBySqlNoApi = async (sqlNo: string) => {
  try {
    const res = await formAxios.get<unknown, MyResponseType<any[]>>('/api/DataSource/GetDataSourcePageByNo', {
      params: { sqlNo: encodeBase64(sqlNo) },
    })
    return res
  } catch (err) {
    httpErrorHandle()
  }
}
