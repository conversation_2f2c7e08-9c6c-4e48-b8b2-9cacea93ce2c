import { projectHttp } from '@/api'
import { useToggle } from '@vueuse/core'
import { ref, toValue, watch } from 'vue'
import type { Template } from '.'

interface UsePublicTemplateOptions {
  immediate?: boolean
}
export function usePublicTemplate(options: UsePublicTemplateOptions = {}) {
  const { immediate = true } = options

  const [httpLoading, httpLoadingToggle] = useToggle()

  const pagination = ref({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    itemCount: 0,
    pageSizes: [5, 7, 10, 15, 20],
    onChange: (page: number) => {
      pagination.value.page = page
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.value.pageSize = pageSize
      pagination.value.page = 1
    },
  })

  const sort = ref({
    sortKey: '',
    sortValue: 'desc',
  })
  const search = ref({
    name: '',
    searchType: '',
  })

  const data = ref<Template[]>([])

  // usage official recommend free star
  const sortOptions = [
    {
      label: '排序方式',
      value: '',
    },
    {
      label: '使用量',
      value: 'usage',
    },
    {
      label: '官方',
      value: 'official',
    },
    {
      label: '推荐',
      value: 'recommend',
    },
    {
      label: '免费',
      value: 'free',
    },
    {
      label: '收藏',
      value: 'star',
    },
    {
      label: '创建时间',
      value: 'createdAt',
    },
    {
      label: '更新时间',
      value: 'updateAt',
    },
  ]

  function update(data: Partial<Template>) {
    httpLoadingToggle()
    return projectHttp({
      url: '/template/update',
      method: 'POST',
      data,
    }).finally(() => {
      httpLoadingToggle()
    })
  }

  function findList() {
    const { page, pageSize } = toValue(pagination)
    const { sortKey, sortValue } = toValue(sort)
    const { name } = toValue(search)
    httpLoadingToggle()
    return projectHttp({
      url: '/template/public/list',
      method: 'GET',
      params: {
        page,
        limit: pageSize,
        sortKey,
        sortValue,
        keyword: name,
      },
    })
      .then(res => {
        const { count, message } = res
        pagination.value.itemCount = count
        data.value = res.data
      })
      .finally(() => {
        httpLoadingToggle()
      })
  }

  function findOne(id: string) {
    httpLoadingToggle()
    return projectHttp({
      url: '/template/public/findone',
      method: 'GET',
      params: {
        id,
      },
    })
      .then(res => {
        return res.data
      })
      .finally(() => {
        httpLoadingToggle()
      })
  }

  function remove(ids: string[] | string) {
    ids = Array.isArray(ids) ? ids : [ids]
    httpLoadingToggle()
    return projectHttp({
      url: '/template/my/delete',
      method: 'POST',
      params: {
        ids: ids.join(','),
      },
    }).finally(() => {
      httpLoadingToggle()
    })
  }

  function release(id: string) {
    httpLoadingToggle()
    return projectHttp({
      url: '/template/release',
      method: 'POST',
      params: {
        id,
      },
    }).finally(() => {
      httpLoadingToggle()
    })
  }

  function unRelease(id: string) {
    httpLoadingToggle()
    return projectHttp({
      url: '/template/unRelease',
      method: 'POST',
      params: {
        id,
      },
    }).finally(() => {
      httpLoadingToggle()
    })
  }

  function usage(templateId: string) {
    httpLoadingToggle()
    return projectHttp({
      url: '/template/createProject',
      method: 'POST',
      params: {
        templateId,
      },
    }).finally(() => {
      httpLoadingToggle()
    })
  }

  function publicUsage(id: string, template: Partial<Template> = {}) {
    httpLoadingToggle()
    return projectHttp({
      url: '/template/public/usage',
      method: 'POST',
      data: {
        id,
        template,
      },
    }).finally(() => {
      httpLoadingToggle()
    })
  }

  if (immediate) {
    findList()
  }

  watch([() => pagination.value.page, () => pagination.value.pageSize, sort.value, search.value], () => {
    findList()
  })

  return {
    update,
    findList,
    httpLoading,
    data,
    pagination,
    sort,
    search,
    sortOptions,
    remove,
    release,
    unRelease,
    usage,
    publicUsage,
    findOne,
  } as const
}
