import { projectHttp } from '@/api'
import { ResultEnum } from '@/enums/httpEnum'
import { httpErrorHandle } from '@/utils'
import type { Chartype } from '@/views/project/items'
import { templateLangT } from './i18n'

export enum TemplateReleaseProgressEnum {
  /**
   * 未发布
   */
  UNPUBLISHED = 1,
  /**
   * 审核中
   */
  AUDITING = 2,
  /**
   * 审核通过
   */
  AUDITED = 3,
  /**
   * 审核不通过
   */
  AUDIT_FAILED = 4,
}

/**
 * 模板
 */
export interface Template {
  /**
   * JSON Schema
   */
  content: string
  /**
   * id
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 首页图片
   */
  indexImage: string
  /**
   * 备注
   */
  remarks: string
  /**
   * 所有者
   */
  owner: TemplateOwner
  /**
   * 来自一个模版/或者一个项目（大屏）
   */
  from: string
  /**
   * 星星
   */
  star: number
  /**
   * 使用次数
   */
  usage: number
  /**
   * 使用需要的积分数量
   */
  score: number
  /**
   * 标签 "数据运营,校园安全"
   */
  tags: string
  /**
   * 分类
   */
  category: TemplateCategory
  /**
   * 分类
   */
  categoryId: string | number
  /**
   * 是否发布
   */
  release: boolean
  /**
   * 发布进度
   */
  releaseProgress: TemplateReleaseProgressEnum
  /**
   * 是否免费
   */
  free: boolean
  /**
   * 是否官方
   */
  official: boolean
  /**
   * 是否推荐
   */
  recommend: boolean
  /**
   * 创建时间
   */
  createdAt: string | number
  /**
   * 更新时间
   */
  updateAt: string | number
}

/**
 * 模板所有者
 */
export interface TemplateOwner {
  /**
   * id
   */
  id: 2
  /**
   * 昵称
   */
  nickname: '周润发'
  /**
   * 头像
   */
  avatar: 'https://goviewpro.goviewlink.com/avatar/2'
}

/**
 * 模板
 */
export interface TemplateCategory {
  /**
   * id
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 图标
   */
  icon: string
  /**
   * 颜色
   */
  color: string
  /**
   * 背景
   */
  background: string
  /**
   * 创建时间
   */
  createdAt: string | number
  /**
   * 更新时间
   */
  updateAt: string | number
}

export async function project2Template(project: Partial<Chartype>, template: Partial<Template>) {
  const { id } = project
  const res = await projectHttp({
    url: '/convertToTemplate',
    method: 'POST',
    data: {
      id,
      template,
    },
  })

  if (res && res.code === ResultEnum.SUCCESS) {
    window['$message'].success(templateLangT('convertToTemplate_success'))
    return
  }
  httpErrorHandle()
}
