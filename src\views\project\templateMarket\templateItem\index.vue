<template>
  <div v-if="cardData" class="go-template-item">
    <n-card hoverable size="small">
      <div class="list-content">
        <!-- 顶部按钮 -->
        <div class="list-content-top">
          <mac-os-control-btn
            class="top-btn"
            :hidden="['remove']"
            @close="emit('delete')"
            @resize="emit('resize')"
          ></mac-os-control-btn>
        </div>
        <!-- 中间 -->
        <div class="list-content-img" @click="resizeHandle">
          <n-image
            object-fit="contain"
            height="180"
            preview-disabled
            :src="cardData.indexImage"
            :alt="cardData.name"
            :fallback-src="requireErrorImg()"
          ></n-image>
        </div>
      </div>
      <template #action>
        <div class="go-flex-items-center list-footer" justify="space-between">
          <n-text class="go-ellipsis-1">
            {{ cardData.name || cardData.id || '未命名' }}
          </n-text>
          <!-- 工具 -->
          <div class="go-flex-items-center list-footer-ri">
            <n-space>
              <template v-for="item in fnBtnList" :key="item.key">
                <template v-if="item.key === 'select'">
                  <n-dropdown
                    trigger="hover"
                    placement="bottom"
                    :options="selectOptions"
                    :show-arrow="true"
                    @select="handleSelect"
                  >
                    <n-button size="small">
                      <template #icon>
                        <component :is="item.icon"></component>
                      </template>
                    </n-button>
                  </n-dropdown>
                </template>

                <n-tooltip v-else placement="bottom" trigger="hover">
                  <template #trigger>
                    <n-button size="small" @click="handleSelect(item.key)">
                      <template #icon>
                        <component :is="item.icon"></component>
                      </template>
                      <span v-if="item.text != null">
                        {{ item.text }}
                      </span>
                    </n-button>
                  </template>
                  <component :is="item.label"></component>
                </n-tooltip>
              </template>
            </n-space>
            <!-- end -->
          </div>
        </div>
        <n-divider style="margin: 16px 0" />
        <n-space justify="space-between" align="center">
          <n-space align="center" :wrap-item="false">
            <n-avatar
              round
              size="small"
              :src="cardData.owner.avatar"
              fallback-src="https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg"
            />
            {{ cardData.owner.nickname || cardData.owner.id || '未知' }}
          </n-space>
          <n-space align="center">
            <n-tag
              v-if="cardData.category"
              :color="{
                color: cardData.category.background,
                textColor: cardData.category.color,
              }"
            >
              {{ cardData.category.name }}
            </n-tag>
            <n-divider vertical style="margin: 0" />
            <n-tag v-if="cardData.recommend" type="primary"> 推荐 </n-tag>
            <n-tag v-if="cardData.official" type="success"> 官方 </n-tag>
            <n-tag v-if="cardData.free" type="warning"> 免费 </n-tag>
          </n-space>
        </n-space>
      </template>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { MacOsControlBtn } from '@/components/Tips/MacOsControlBtn'
import { icon } from '@/plugins'
import type { Template } from '@/template'
import { templateLangRender, templateLangT } from '@/template/i18n'
import { useMyTemplate } from '@/template/useMyTemplate'
import { usePublicTemplate } from '@/template/usePublicTemplate'
import { renderIcon, renderLang, requireErrorImg } from '@/utils'
import { useDialog, useMessage } from 'naive-ui'
import type { MaybeRefOrGetter, PropType, VNode } from 'vue'
import { computed, reactive, ref, toValue } from 'vue'

const { usage, publicUsage } = usePublicTemplate({
  immediate: false,
})
const {
  EllipsisHorizontalCircleSharpIcon,
  CopyIcon,
  TrashIcon,
  PencilIcon,
  DownloadIcon,
  BrowsersOutlineIcon,
  HammerIcon,
  SendIcon,
  StarIcon,
} = icon.ionicons5

const emit = defineEmits(['usage', 'release', 'unRelease', 'edit', 'delete', 'preview', 'resize'])

const message = useMessage()
const dialog = useDialog()

const props = defineProps({
  cardData: Object as PropType<Template>,
})

const isRelease = computed(() => {
  return props.cardData?.release
})

interface ActionItem {
  label: string | (() => VNode)
  text?: MaybeRefOrGetter<string | number>
  key: string
  icon: string | (() => VNode)
  onClick?: () => void
}

const fnBtnList = reactive<ActionItem[]>([
  {
    label: templateLangRender('usage'),
    text: computed(() => props.cardData!.usage),
    key: 'usage',
    icon: renderIcon(CopyIcon),
    onClick: async () => {
      await publicUsage(props.cardData!.id, {
        name: props.cardData!.name,
      })
      props.cardData!.usage++
      message.success(templateLangT('usagePublicSuccess'))
    },
  },
  {
    label: templateLangRender('star'),
    text: computed(() => props.cardData!.star),
    key: 'star',
    icon: renderIcon(StarIcon),
    onClick: () => {
      alert('TODO: star')
    },
  },
])

const selectOptions = ref<ActionItem[]>([])

const handleSelect = (key: string) => {
  const item = [...fnBtnList, ...selectOptions.value].find(item => item.key === key)
  if (item?.onClick) {
    item.onClick()
  }
}
</script>

<style lang="scss" scoped>
$contentHeight: 180px;
@include go('template-item') {
  position: relative;
  border-radius: $--border-radius-base;
  border: 1px solid rgba(0, 0, 0, 0);
  @extend .go-transition;
  &:hover {
    @include hover-border-color('hover-border-color');
  }
  .list-content {
    margin-top: 20px;
    margin-bottom: 5px;
    cursor: pointer;
    border-radius: $--border-radius-base;
    @include background-image('background-point');
    @extend .go-point-bg;
    &-top {
      position: absolute;
      top: 10px;
      left: 10px;
      height: 22px;
    }
    &-img {
      height: $contentHeight;
      @extend .go-flex-center;
      @extend .go-border-radius;
      @include deep() {
        img {
          @extend .go-border-radius;
        }
      }
    }
  }
  .list-footer {
    flex-wrap: nowrap;
    justify-content: space-between;
    line-height: 30px;
    &-ri {
      justify-content: flex-end;
      min-width: 180px;
    }
  }
}
</style>
