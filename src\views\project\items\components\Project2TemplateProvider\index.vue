<script setup lang="ts">
import { project2Template, type Template } from '@/template'
import { useToggle } from '@vueuse/core'
import { computed, provide, ref, toValue } from 'vue'
import { useProject2TemplateInjectionKey } from './useProject2Template'
import type { Chartype } from '../..'
import { useTemplateCategory } from '@/template/useTemplateCategory'

const [showModal, showModalToggle] = useToggle()

const currForm = ref<Partial<Template>>({
  name: '模版1',
})

const projectRef = ref<Partial<Chartype>>({})

const confirmHandler = async () => {
  await project2Template(toValue(projectRef), toValue(currForm))
  showModalToggle(false)
}

const rules = ref({})

provide(useProject2TemplateInjectionKey, {
  project2Template: project => {
    projectRef.value = project
    currForm.value = {
      name: project.title,
      indexImage: project.image,
    }
    showModalToggle(true)
  },
})
const tagsRef = computed({
  get: () => {
    const { tags } = currForm.value
    if (tags) {
      return tags.split(',')
    }
    return []
  },
  set: val => {
    currForm.value.tags = val.join(',')
  },
})
const { httpLoading, data, search } = useTemplateCategory()

const categoryOptions = computed(() => {
  return data.value.map(item => {
    return {
      label: item.name,
      value: item.id,
    }
  })
})

const handleSearch = async (value: string) => {
  search.value.name = value.trim()
}
</script>

<template>
  <slot></slot>
  <n-modal v-model:show="showModal">
    <n-card style="width: 600px" title="模版" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-form ref="formRef" :label-width="80" :model="currForm" :rules="rules" label-placement="left">
        <n-form-item label="名称" path="name">
          <n-input v-model:value="currForm.name" />
        </n-form-item>

        <n-form-item label="封面" path="indexImage">
          <n-input v-model:value="currForm.indexImage" />
        </n-form-item>

        <n-form-item label="分类" path="categoryId">
          <n-select
            v-model:value="currForm.categoryId"
            filterable
            :options="categoryOptions"
            :loading="httpLoading"
            clearable
            remote
            @search="handleSearch"
          />
        </n-form-item>

        <n-form-item label="标签" path="tags">
          <n-dynamic-tags v-model:value="tagsRef" />
        </n-form-item>

        <n-form-item label="是否免费" path="free">
          <n-switch v-model:value="currForm.free" />
        </n-form-item>

        <n-form-item label="是否官方" path="official">
          <n-switch v-model:value="currForm.official" />
        </n-form-item>

        <n-form-item label="是否推荐" path="recommend">
          <n-switch v-model:value="currForm.recommend" />
        </n-form-item>

        <n-form-item label="备注" path="remarks">
          <n-input
            v-model:value="currForm.remarks"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button type="primary" @click="showModalToggle(false)">取消</n-button>
          <n-button type="primary" @click="confirmHandler">确定</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped>
/* Your styles go here */
</style>
