import { inject, type InjectionKey } from 'vue'
import type { Chartype } from '../..'

interface useProject2TemplateInjectionType {
  project2Template(project: Chartype): void
}
export const useProject2TemplateInjectionKey = Symbol() as InjectionKey<useProject2TemplateInjectionType>

export function useProject2Template() {
  const api = inject(useProject2TemplateInjectionKey)
  if (api === undefined) {
    throw new Error('useProject2Template is not provided')
  }

  return {
    ...api,
  } as const
}
