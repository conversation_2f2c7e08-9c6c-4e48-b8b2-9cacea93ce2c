<script lang="ts" setup>
import { ref, computed } from 'vue'
import { icon } from '@/plugins'
import { useTargetData } from '../../../hooks/useTargetData.hook'
import { SettingItemBox, SettingItem } from '@/components/Pages/ChartItemSetting'
import { useDesignStore } from '@/store/modules/designStore/designStore'
import { ChartDataMatchingAndShow } from '../ChartDataMatchingAndShow'
import { ChartDataSourceControl } from './components/ChartDataSourceControl'
import { getDataSourceBySqlNoApi } from '@/api/path'
import { newFunctionHandle } from '@/utils'

const { HelpOutlineIcon, FlashIcon, PulseIcon, FishIcon } = icon.ionicons5
const { targetData, chartEditStore } = useTargetData()
const designStore = useDesignStore()

const loading = ref(false)
const controlModel = ref(false)
const showMatching = ref(false)

// 颜色
const themeColor = computed(() => {
  return designStore.getAppTheme
})

// 请求配置 model
const controlModelHandle = () => {
  controlModel.value = true
}

// 发送请求
const sendHandle = async () => {
  if (!targetData.value?.request.requestDataSourceSQLNo) return
  loading.value = true
  try {
    const res = await getDataSourceBySqlNoApi(targetData.value.request.requestDataSourceSQLNo)
    loading.value = false
    if (res) {
      const { data } = res
      if (!data && !targetData.value.filter) {
        window['$message'].warning('您的数据不符合默认格式，请配置处理器！')
        showMatching.value = true
        return
      }
      targetData.value.option.dataset = newFunctionHandle(data, res, targetData.value.filter)
      showMatching.value = true
      return
    }
    window['$message'].warning('没有拿到返回值，请检查接口！')
  } catch (error) {}
}
</script>

<template>
  <!-- 选中内容 -->
  <div class="go-chart-data-source">
    <n-card class="n-card-shallow">
      <setting-item-box name="标识" :alone="true">
        <setting-item name="数据源标识">
          <n-input size="small" :placeholder="targetData.request.requestDataSourceSQLNo || '暂无'" :disabled="true">
            <template #prefix>
              <n-icon :component="FishIcon" />
            </template>
          </n-input>
        </setting-item>
      </setting-item-box>

      <div class="edit-text" @click="controlModelHandle">
        <div class="go-absolute-center">
          <n-button type="primary" secondary>编辑配置</n-button>
        </div>
      </div>
    </n-card>
  </div>

  <setting-item-box :alone="true">
    <template #name>
      测试
      <n-tooltip trigger="hover">
        <template #trigger>
          <n-icon size="21" :depth="3">
            <help-outline-icon></help-outline-icon>
          </n-icon>
        </template>
        默认赋值给 dataset 字段
      </n-tooltip>
    </template>
    <n-button type="primary" ghost @click="sendHandle">
      <template #icon>
        <n-icon>
          <flash-icon />
        </n-icon>
      </template>
      发送请求
    </n-button>
  </setting-item-box>

  <!-- 底部数据展示 -->
  <chart-data-matching-and-show :show="showMatching && !loading" :ajax="true"></chart-data-matching-and-show>

  <!-- 骨架图 -->
  <go-skeleton :load="loading" :repeat="3"></go-skeleton>

  <!-- 编辑 / 新增弹窗 -->
  <chart-data-source-control v-model:modelShow="controlModel" @sendHandle="sendHandle"></chart-data-source-control>
</template>

<style lang="scss" scoped>
@include go('chart-data-source') {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  .n-card-shallow {
    &.n-card {
      @extend .go-background-filter;
      @include deep() {
        .n-card__content {
          padding: 10px;
        }
      }
    }
    .edit-text {
      position: absolute;
      top: 0px;
      left: 0px;
      width: 325px;
      height: 100px;
      cursor: pointer;
      opacity: 0;
      transition: all 0.3s;
      @extend .go-background-filter;
      backdrop-filter: blur(2px) !important;
    }
    &:hover {
      border-color: v-bind('themeColor');
      .edit-text {
        opacity: 1;
      }
    }
  }
}
</style>
