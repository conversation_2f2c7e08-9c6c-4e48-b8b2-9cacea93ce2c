import { PageEnum } from '@/enums/pageEnum'
import type { RouteRecordRaw } from 'vue-router'

export const templateRoutes = {
  templateCategory: {
    path: '/project/management/template-category',
    name: 'project-management-template-category',
    component: () => import('@/views/project/management/templateCategory/index.vue'),
    meta: {
      title: '模板分类',
      documentTitle: '模板分类管理',
    },
  },
  templateRelease: {
    path: '/project/management/template-release',
    name: 'project-management-template-release',
    component: () => import('@/views/project/management/templateRelease/index.vue'),
    meta: {
      title: '模版发布',
      documentTitle: '模版发布管理',
    },
  },
  templateUsage: {
    props: true,
    path: '/project/template-usage/:templateId',
    name: 'project-template-usage',
    component: () => import('@/views/project/templateMarket/index.vue'),
    meta: {
      title: '模板市场',
      menuKey: PageEnum.BASE_HOME_TEMPLATE_MARKET_NAME,
    },
  },
} satisfies Record<string, RouteRecordRaw>

export const templateRouteList = [
  templateRoutes.templateCategory,
  templateRoutes.templateRelease,
  templateRoutes.templateUsage,
] satisfies RouteRecordRaw[]
