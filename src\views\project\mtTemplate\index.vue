<script setup lang="ts">
import type { TemplateCategory } from '@/template'
import { useMyTemplate } from '@/template/useMyTemplate'
import { useToggle } from '@vueuse/core'
import { NButton } from 'naive-ui'
import { ref } from 'vue'
import { TemplateItem } from './templateItem'

const { httpLoading, findList, data, pagination, sort, search, sortOptions, remove, update } = useMyTemplate()

const onUpdate = async () => {
  showModalToggle(true)
}
const confirmHandler = async () => {
  await update(currForm.value)
  await findList()
  showModalToggle(false)
}

const [showModal, showModalToggle] = useToggle()

const currForm = ref<Partial<TemplateCategory>>({
  name: '模版1',
  icon: '',
  color: 'rgba(255, 255, 255, 0.82)',
  background: 'rgba(30, 30, 31, 1)',
  sort: 0,
})

const rules = ref({})

const deleteSuccessHandle = () => {
  findList()
}
</script>
<template>
  <div class="go-project-my-template">
    <n-spin :show="httpLoading">
      <n-space justify="space-between" style="padding: 10px">
        <n-space class="toolbar" justify="space-around"> </n-space>
        <n-space justify="space-around">
          <n-space justify="space-around">
            <n-input v-model:value="search.name" type="text" placeholder="输入搜索" />
          </n-space>
          <n-space align="center">
            <n-select v-model:value="sort.sortKey" :options="sortOptions" style="width: 100px" />
            <n-switch v-model:value="sort.sortValue" :round="false" checked-value="asc" unchecked-value="desc">
              <template #checked>升序</template>
              <template #unchecked>降序</template>
            </n-switch>
          </n-space>
        </n-space>
      </n-space>
      <n-grid :x-gap="20" :y-gap="20" cols="2 s:2 m:3 l:4 xl:4 xxl:4" responsive="screen">
        <n-grid-item v-for="(item, index) in data" :key="item.id">
          <TemplateItem
            :cardData="item"
            @preview="previewHandle"
            @resize="resizeHandle"
            @delete="deleteHandle(item)"
            @release="releaseHandle(item, index)"
            @edit="editHandle"
            @delete_success="deleteSuccessHandle"
          ></TemplateItem>
        </n-grid-item>
      </n-grid>
    </n-spin>
    <n-modal v-model:show="showModal">
      <n-card style="width: 600px" title="模态框" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <n-space justify="center" align="center" style="margin-bottom: 20px">
          <div
            :style="{
              background: currForm.background,
              color: currForm.color,
              padding: '6px 10px',
              borderRadius: '4px',
            }"
          >
            {{ currForm.icon }}
            {{ currForm.name || '示例' }}
          </div>
        </n-space>

        <n-form ref="formRef" :label-width="80" :model="currForm" :rules="rules" label-placement="left">
          <n-form-item label="名称" path="name">
            <n-input v-model:value="currForm.name" />
          </n-form-item>
          <n-form-item label="颜色" path="color">
            <n-color-picker v-model:value="currForm.color" style="width: 180px" />
          </n-form-item>
          <n-form-item label="背景色" path="background">
            <n-color-picker v-model:value="currForm.background" style="width: 180px" />
          </n-form-item>
          <n-form-item label="图标" path="icon">
            <n-input v-model:value="currForm.icon" />
          </n-form-item>
          <n-form-item label="排序" path="sort">
            <n-input-number v-model:value="currForm.sort" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="showModalToggle(false)">取消</n-button>
            <n-button type="primary" @click="confirmHandler">确定</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped>
@include go(project-my-template) {
  padding: 20px;
}
</style>
