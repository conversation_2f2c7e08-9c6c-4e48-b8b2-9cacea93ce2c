import type { AxiosResponse, AxiosRequestConfig, Axios, AxiosError, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import { RequestHttpHeaderEnum, ResultEnum, ModuleTypeEnum, ContentTypeEnum } from '@/enums/httpEnum'
import { PageEnum, ErrorPageNameMap } from '@/enums/pageEnum'
import { StorageEnum } from '@/enums/storageEnum'
import { axiosPre } from '@/settings/httpSetting'
import { SystemStoreEnum, SystemStoreUserInfoEnum } from '@/store/modules/systemStore/systemStore.d'
import { redirectErrorPage, getLocalStorage, routerTurnByName, isPreview } from '@/utils'
import { fetchAllowList } from './axios.config'
import includes from 'lodash/includes'
import { isInnerModule } from './path'
import { runtimeEnv } from '@/runtime-env'
import { mergeWith } from 'lodash'

export interface MyResponseType<T> {
  code: ResultEnum
  data: T
  message: string
}

export interface MyRequestInstance extends Axios {
  <T = any>(config: AxiosRequestConfig): Promise<MyResponseType<T>>
}

const axiosInstance = axios.create({
  baseURL: `${import.meta.env.PROD ? runtimeEnv.VITE_PRO_PATH : ''}${axiosPre}`,
  timeout: ResultEnum.TIMEOUT,
}) as unknown as MyRequestInstance

function requestInterceptor(config: InternalAxiosRequestConfig) {
  // 白名单校验
  if (includes(fetchAllowList, config.url)) return config
  // 获取 token
  const info = getLocalStorage(StorageEnum.GO_SYSTEM_STORE)
  // 重新登录
  if (!info) {
    routerTurnByName(PageEnum.BASE_LOGIN_NAME)
    return config
  }
  const userInfo = info[SystemStoreEnum.USER_INFO]
  config.headers[userInfo[SystemStoreUserInfoEnum.TOKEN_NAME] || 'token'] =
    userInfo[SystemStoreUserInfoEnum.USER_TOKEN] || ''
  return config
}

axiosInstance.interceptors.request.use(requestInterceptor, (err: AxiosError) => {
  Promise.reject(err)
})

function responseInterceptor(res: AxiosResponse) {
  // 预览页面和第三方数据接口错误不进行处理
  if (isPreview() || !isInnerModule(res.request.responseURL)) {
    return Promise.resolve(res.data)
  }
  const { code } = res.data as { code: number }

  if (code === undefined || code === null) return Promise.resolve(res.data)

  // 成功
  if (code === ResultEnum.SUCCESS || code === 1) {
    return Promise.resolve(res.data)
  }

  // 登录过期
  if (code === ResultEnum.TOKEN_OVERDUE) {
    window['$message'].error(window['$t']('http.token_overdue_message'))
    routerTurnByName(PageEnum.BASE_LOGIN_NAME)
    return Promise.resolve(res.data)
  }

  // 固定错误码重定向
  if (ErrorPageNameMap.get(code)) {
    redirectErrorPage(code)
    return Promise.resolve(res.data)
  }

  // 提示错误
  window['$message'].error(window['$t']((res.data as any).msg))
  return Promise.resolve(res.data)
}

// 响应拦截器
axiosInstance.interceptors.response.use(responseInterceptor, (err: AxiosError) => {
  const status = err.response?.status
  switch (status) {
    case 401:
      routerTurnByName(PageEnum.BASE_LOGIN_NAME)
      Promise.reject(err)
      break

    default:
      Promise.reject(err)
      break
  }
})

const inlineRequestConfig: AxiosRequestConfig = {
  timeout: ResultEnum.TIMEOUT,
  headers: {
    'Content-Type': ContentTypeEnum.JSON,
  },
}

export function createAxios(config: AxiosRequestConfig) {
  function customizer(objValue: any, srcValue: any) {
    if (Array.isArray(objValue)) {
      return objValue.concat(srcValue)
    }
    if (typeof objValue === 'object') {
      return customizer(objValue, srcValue)
    }
    return srcValue
  }
  const _config = mergeWith(inlineRequestConfig, config, customizer)
  const instance = axios.create(_config) as unknown as MyRequestInstance
  instance.interceptors.request.use(requestInterceptor, (err: AxiosError) => {
    Promise.reject(err)
  })
  instance.interceptors.response.use(responseInterceptor, (err: AxiosError) => {
    const status = err.response?.status
    switch (status) {
      case 401:
        routerTurnByName(PageEnum.BASE_LOGIN_NAME)
        Promise.reject(err)
        break

      default:
        Promise.reject(err)
        break
    }
  })
  return instance
}

const formAxios = createAxios({
  baseURL: import.meta.env.PROD ? runtimeEnv.VITE_PRO_PATH : import.meta.env.VITE_DEV_PATH,
})

export { formAxios }

export default axiosInstance
