<script lang="ts" setup>
import { ChartDataSourceConfig } from '../ChartDataSourceConfig/index'
import { useTargetData } from '@/views/chart/ContentConfigurations/components/hooks/useTargetData.hook'
import { computed } from 'vue'

const emit = defineEmits(['sendHandle'])

const modelShowRef = defineModel('modelShow', { type: Boolean, default: false })

const { targetData } = useTargetData()
const chartConfig = computed(() => targetData.value.chartConfig)

const closeHandle = () => {
  modelShowRef.value = false
}

const onEsc = () => {
  closeHandle()
}

const closeAndSendHandle = () => {
  modelShowRef.value = false
  emit('sendHandle')
}
</script>

<template>
  <n-modal
    class="go-chart-data-source-control"
    v-model:show="modelShowRef"
    :mask-closable="false"
    :close-on-esc="true"
    :onEsc="onEsc"
  >
    <n-card :bordered="false" role="dialog" size="small" aria-modal="true" style="width: 900px; height: 650px">
      <template #header></template>
      <template #header-extra> </template>
      <div class="data-source-content">
        <chart-data-source-config :target-data-request="targetData?.request"></chart-data-source-config>
      </div>
      <!-- 底部 -->
      <template #action>
        <n-space justify="space-between">
          <div>
            <n-text>「 {{ chartConfig?.categoryName }} 」</n-text>
            <n-text>—— </n-text>
            <n-tag type="primary" :bordered="false" style="border-radius: 5px"> 数据源 </n-tag>
          </div>
          <div>
            <n-button class="go-mr-3" @click="closeHandle">取消</n-button>
            <n-button type="primary" @click="closeAndSendHandle">保存</n-button>
          </div>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<style lang="css" scoped></style>
