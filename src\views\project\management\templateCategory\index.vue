<script setup lang="ts">
import type { TemplateCategory } from '@/template'
import { useTemplateCategory } from '@/template/useTemplateCategory'
import { useToggle } from '@vueuse/core'
import type { DataTableColumns } from 'naive-ui'
import { NButton } from 'naive-ui'
import { h, ref } from 'vue'

const { httpLoading, add, findList, data, pagination, sort, search, sortOptions, remove } = useTemplateCategory()

const columns = [
  { title: '名称', key: 'name' },
  { title: '排序', key: 'sort' },
  { title: '背景色', key: 'background' },
  { title: '颜色', key: 'color' },
  { title: '图标', key: 'icon' },
  { title: '创建时间', key: 'createdAt' },
  { title: '修改时间', key: 'updateAt' },
  {
    title: '操作',
    key: 'action',
    width: 200,
    render(row) {
      return [
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            size: 'small',
            onClick: async () => {
              await remove(row.id)
              await findList()
            },
          },
          { default: () => 'Delete' },
        ),
      ]
    },
  },
] satisfies DataTableColumns<TemplateCategory>

const onAdd = async () => {
  showModalToggle(true)
}
const confirmHandler = async () => {
  await add(currForm.value)
  await findList()
  showModalToggle(false)
}

const [showModal, showModalToggle] = useToggle()

const currForm = ref<Partial<TemplateCategory>>({
  name: '模版1',
  icon: '',
  color: 'rgba(255, 255, 255, 0.82)',
  background: 'rgba(30, 30, 31, 1)',
  sort: 0,
})

const rules = ref({})
</script>
<template>
  <n-spin :show="httpLoading">
    <div class="go-project-management-category">
      <n-space justify="space-between" style="padding: 10px">
        <n-space class="toolbar" justify="space-around">
          <n-button type="primary" @click="onAdd"> 新增 </n-button>
        </n-space>
        <n-space justify="space-around">
          <n-space justify="space-around">
            <n-input v-model:value="search.name" type="text" placeholder="输入搜索" />
          </n-space>
          <n-space align="center">
            <n-select v-model:value="sort.sortKey" :options="sortOptions" style="width: 100px" />
            <n-switch v-model:value="sort.sortValue" :round="false" checked-value="asc" unchecked-value="desc">
              <template #checked>升序</template>
              <template #unchecked>降序</template>
            </n-switch>
          </n-space>
        </n-space>
      </n-space>
      <n-data-table remote :columns="columns" :data="data" :pagination="pagination" />
    </div>
  </n-spin>
  <n-modal v-model:show="showModal">
    <n-card style="width: 600px" title="模态框" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-space justify="center" align="center" style="margin-bottom: 20px">
        <div
          :style="{
            background: currForm.background,
            color: currForm.color,
            padding: '6px 10px',
            borderRadius: '4px',
          }"
        >
          {{ currForm.icon }}
          {{ currForm.name || '示例' }}
        </div>
      </n-space>

      <n-form ref="formRef" :label-width="80" :model="currForm" :rules="rules" label-placement="left">
        <n-form-item label="名称" path="name">
          <n-input v-model:value="currForm.name" />
        </n-form-item>
        <n-form-item label="颜色" path="color">
          <n-color-picker v-model:value="currForm.color" style="width: 180px" />
        </n-form-item>
        <n-form-item label="背景色" path="background">
          <n-color-picker v-model:value="currForm.background" style="width: 180px" />
        </n-form-item>
        <n-form-item label="图标" path="icon">
          <n-input v-model:value="currForm.icon" />
        </n-form-item>
        <n-form-item label="排序" path="sort">
          <n-input-number v-model:value="currForm.sort" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button type="primary" @click="showModalToggle(false)">取消</n-button>
          <n-button type="primary" @click="confirmHandler">确定</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<style lang="scss" scoped>
@include go(project-management-category) {
  padding: 20px;
}
</style>
