export interface DataSourceItem {
  id: string
  dataType: number
  sqlNo: string
  remark: string
  category: string
  typeCode: string
  typeName: string
  dbContext: string
  sqlConfig: string
  sqlParameters: string
  sqlText: string
  host: string
  method: string
  methodName: string
  params: string
  parentField: string
  valueField: string
  textField: string
  top: string
  sort: string
  fieldConfigs: FieldConfigsInDataSource
}

export interface FieldConfigsInDataSource {
  pairs: Array<{
    key: string
    label: string
    value: string
    require: boolean
  }>
  fields: Array<{
    code: string
    name: string
    width: string
    isFilter: boolean
    isHide: boolean
  }>
  headers: Array<{
    code: string
  }>
}
