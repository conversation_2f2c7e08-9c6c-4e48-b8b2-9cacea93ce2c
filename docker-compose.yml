version: '3.4'

services:
  iduo-go-view-local:
    image: iduo-go-view
    container_name: iduo-go-view
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - 9528:80
    restart: always
    network_mode: bridge
    volumes:
      - ./dist:/usr/share/nginx/html/
      # http
      - ./nginx.conf:/etc/nginx/nginx.conf
      # or
      # https SSL
      # - ./nginx.ssl.conf:/etc/nginx/nginx.conf
      # - ./ssl/:/etc/nginx/ssl/

  iduo-go-view:
    image: registry.cn-hangzhou.aliyuncs.com/yiduo/web:iduo-go-view-dev
    container_name: iduo-go-view
    ports:
      - 9528:80
    restart: always
    network_mode: bridge
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./dist/config.js:/usr/share/nginx/html/config.js
