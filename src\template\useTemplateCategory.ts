import { projectHttp } from '@/api'
import { useToggle } from '@vueuse/core'
import { ref, toValue, watch } from 'vue'
import type { TemplateCategory } from '.'

interface UseTemplateCategoryOptions {
  immediate?: boolean
}
export function useTemplateCategory(options: UseTemplateCategoryOptions = {}) {
  const { immediate = true } = options

  const [httpLoading, httpLoadingToggle] = useToggle()

  const pagination = ref({
    page: 1,
    pageSize: 5,
    showSizePicker: true,
    itemCount: 0,
    pageSizes: [5, 7, 10, 15, 20],
    onChange: (page: number) => {
      pagination.value.page = page
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.value.pageSize = pageSize
      pagination.value.page = 1
    },
  })

  const sort = ref({
    sortKey: '',
    sortValue: 'desc',
  })
  const search = ref({
    name: '',
  })

  const data = ref<TemplateCategory[]>([])

  const sortOptions = [
    {
      label: '排序方式',
      value: '',
    },
    {
      label: '排序列',
      value: 'sort',
    },
    {
      label: '创建时间',
      value: 'createdAt',
    },
    {
      label: '更新时间',
      value: 'updateAt',
    },
  ]

  function add(data: Partial<TemplateCategory>) {
    httpLoadingToggle()
    return projectHttp({
      url: '/category/add',
      method: 'POST',
      data,
    }).finally(() => {
      httpLoadingToggle()
    })
  }

  function findList() {
    const { page, pageSize } = toValue(pagination)
    const { sortKey, sortValue } = toValue(sort)
    const { name } = toValue(search)
    httpLoadingToggle()
    return projectHttp({
      url: '/category/list',
      method: 'GET',
      params: {
        page,
        limit: pageSize,
        sortKey,
        sortValue,
        keyword: name,
      },
    })
      .then(res => {
        const { count, message } = res
        pagination.value.itemCount = count
        data.value = res.data
      })
      .finally(() => {
        httpLoadingToggle()
      })
  }

  function remove(ids: string[] | string) {
    ids = Array.isArray(ids) ? ids : [ids]
    httpLoadingToggle()
    return projectHttp({
      url: '/category/delete',
      method: 'POST',
      params: {
        ids: ids.join(','),
      },
    }).finally(() => {
      httpLoadingToggle()
    })
  }

  if (immediate) {
    findList()
  }

  watch([() => pagination.value.page, () => pagination.value.pageSize, sort.value, search.value], () => {
    findList()
  })

  return {
    add,
    findList,
    httpLoading,
    data,
    pagination,
    sort,
    search,
    sortOptions,
    remove,
  } as const
}
