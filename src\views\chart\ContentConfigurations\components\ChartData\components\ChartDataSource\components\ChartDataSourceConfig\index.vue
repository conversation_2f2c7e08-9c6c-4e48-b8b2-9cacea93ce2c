<script lang="ts" setup>
import type { RequestConfigType } from '@/store/modules/chartEditStore/chartEditStore.d'
import type { PropType } from 'vue'
import { SettingItemBox, SettingItem } from '@/components/Pages/ChartItemSetting'
import { toRefs, ref, onMounted, computed, watch, h } from 'vue'
import { getDataSourceByPageApi } from '@/api/path'
import type { DataSourceItem } from '@/api/path/form'
import { NInput, type DataTableColumns } from 'naive-ui'

const props = defineProps({
  targetDataRequest: Object as PropType<RequestConfigType>,
})

const { requestDataSourceSQLNo } = toRefs(props.targetDataRequest as RequestConfigType)
const datasourceOptions = ref<Array<{ label: string; value: string; source: DataSourceItem }>>([])
const currentDatasource = computed(() => {
  const sqlNo = requestDataSourceSQLNo.value?.split('$')[0]
  if (!sqlNo) return null
  const datasource = datasourceOptions.value.find(item => item.value === sqlNo)?.source
  return datasource ? datasource : null
})

watch(
  () => currentDatasource.value?.fieldConfigs.pairs,
  value => {
    if (!value) return
    if (value.every(item => !item.value)) {
      requestDataSourceSQLNo.value = requestDataSourceSQLNo.value?.replace(/~/g, '') || ''
    }
  },
  { deep: true },
)

const datasourceTableColumns: DataTableColumns = [
  {
    key: 'label',
    title: '参数名',
  },
  {
    key: 'require',
    title: '是否必填',
    render: (row: any) => {
      return row.require ? '是' : '否'
    },
  },
  {
    key: 'value',
    title: '参数值',
    render: (row: any, index: number) => {
      const pairs = requestDataSourceSQLNo.value?.split('$')?.[1].split('~') || []
      return h(NInput, {
        value: row.value || pairs[index],
        onUpdateValue(v) {
          row.value = v
          const pairs = currentDatasource.value?.fieldConfigs.pairs
          const sqlNo = requestDataSourceSQLNo.value?.split('$')[0]
          requestDataSourceSQLNo.value = `${sqlNo}$${pairs?.map(item => item.value).join('~')}`
        },
      })
    },
  },
]

onMounted(async () => {
  const datasource = await getDataSourceByPageApi({
    pageIndex: 1,
    pageSize: 9999,
  })
  if (!datasource) return
  const [sqlNo, pairs] = requestDataSourceSQLNo.value?.split('$') || []
  if (sqlNo && pairs) {
    const currentDatasource = datasource.data.find(item => item.sqlNo === sqlNo)
    const pairsList = pairs.split('~')
    if (currentDatasource) {
      currentDatasource.fieldConfigs.pairs = currentDatasource.fieldConfigs.pairs.map((item, index) => {
        return {
          ...item,
          value: pairsList[index],
        }
      })
    }
  }

  datasourceOptions.value = datasource.data.map(item => {
    return {
      label: `${item.sqlNo} - ${item.remark}`,
      value: item.sqlNo,
      source: item,
    }
  })
})
</script>

<template>
  <setting-item-box name="标识" style="padding-right: 25px" alone>
    <setting-item name="数据源标识">
      <n-select
        :value="requestDataSourceSQLNo"
        @update:value="
          (value: string) => {
            requestDataSourceSQLNo = `${value}$`
          }
        "
        filterable
        placeholder="选择数据源"
        :options="datasourceOptions"
      />
    </setting-item>
  </setting-item-box>
  <setting-item-box name="参数解析" style="padding-right: 25px" alone>
    <setting-item>
      <n-data-table
        :columns="datasourceTableColumns"
        :data="currentDatasource?.fieldConfigs.pairs"
        :pagination="false"
        :bordered="false"
        :max-height="250"
      />
    </setting-item>
  </setting-item-box>
</template>

<style lang="css" scoped></style>
