<script setup lang="ts">
import type { Template } from '@/template'
import { templateLangT } from '@/template/i18n'
import { usePublicTemplate } from '@/template/usePublicTemplate'
import { useToggle } from '@vueuse/core'
import { NButton, useMessage } from 'naive-ui'
import { onMounted, ref } from 'vue'
import { TemplateItem } from './templateItem'

const { httpLoading, findList, data, pagination, sort, search, sortOptions, update, findOne, publicUsage } =
  usePublicTemplate()

const message = useMessage()
const onUpdate = async () => {
  showModalToggle(true)
}
const confirmHandler = async () => {
  await update(currForm.value)
  await findList()
  showModalToggle(false)
}
const props = defineProps({
  templateId: String,
})

const [showModal, showModalToggle] = useToggle()
onMounted(async () => {
  if (props.templateId) {
    const data = await findOne(props.templateId)
    currForm.value = data
    showModalToggle(true)
  }
})

const currForm = ref<Template>()

const rules = ref({})

const publicTemplateUsageHandler = async () => {
  const item = currForm.value
  if (!item) {
    return
  }
  const itemInList = data.value.find(i => i.id === item.id)
  await publicUsage(item.id, {
    name: item.name,
  })
  item.usage++

  if (itemInList) {
    itemInList.usage++
  }
  message.success(templateLangT('usagePublicSuccess'))
  showModalToggle(false)
}
</script>
<template>
  <div class="go-project-template-market">
    <n-spin :show="httpLoading">
      <n-space justify="space-between" style="padding: 10px">
        <n-space class="toolbar" justify="space-around"> </n-space>
        <n-space justify="space-around">
          <n-space justify="space-around">
            <n-input v-model:value="search.name" type="text" placeholder="输入搜索" />
          </n-space>
          <n-space align="center">
            <n-select v-model:value="sort.sortKey" :options="sortOptions" style="width: 100px" />
            <n-switch v-model:value="sort.sortValue" :round="false" checked-value="asc" unchecked-value="desc">
              <template #checked>升序</template>
              <template #unchecked>降序</template>
            </n-switch>
          </n-space>
        </n-space>
      </n-space>
      <n-grid :x-gap="20" :y-gap="20" cols="2 s:2 m:3 l:4 xl:4 xxl:4" responsive="screen">
        <n-grid-item v-for="(item, index) in data" :key="item.id">
          <TemplateItem
            :cardData="item"
            @preview="previewHandle"
            @resize="resizeHandle"
            @delete="deleteHandle(item)"
            @release="releaseHandle(item, index)"
            @edit="editHandle"
          ></TemplateItem>
        </n-grid-item>
      </n-grid>
    </n-spin>
    <n-modal v-model:show="showModal">
      <n-card style="width: 800px" title="模版详情" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <TemplateItem :cardData="currForm"></TemplateItem>
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="showModalToggle(false)">继续浏览</n-button>
            <n-button type="primary" @click="publicTemplateUsageHandler">使用</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped>
@include go(project-template-market) {
  padding: 20px;
}
</style>
