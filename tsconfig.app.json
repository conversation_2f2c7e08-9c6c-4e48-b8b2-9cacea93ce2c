{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "types/**/*"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "types": ["naive-ui/volar"],

    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    // TODO: waiting fix @typescript-eslint/consistent-type-imports
    "verbatimModuleSyntax": false
  }
}
