import { remoteConfig } from '@/remote-config'

export interface RuntimeEnv extends Partial<ImportMetaEnv> {
  VITE_PRO_PATH: string
}

const safeEnvKeys = ['VITE_PRO_PATH']

export const runtimeEnv: RuntimeEnv = {
  VITE_PRO_PATH: ''
}

safeEnvKeys.forEach(key => {
  runtimeEnv[key] = import.meta.env[key]
  const value = remoteConfig.runtimeEnv?.[key]
  if (value !== undefined) {
    runtimeEnv[key] = value
  }
})
