export const en = {
  convertToTemplate: 'Convert to Template',
  convertToTemplate_success: 'Convert success',
  convertToTemplate_failure: 'Convert failure',
  templateCategory: 'Template Category',
  templateRelease: 'Template Release',
  templateManagement: 'Management',
  released: 'Released',
  unreleased: 'Unreleased',
  usage: 'Usage',
  edit: 'Edit',
  release: 'Release',
  unRelease: 'UnRelease',
  star: 'Star',
  usagePublicSuccess: 'Usage success',
} as const

type LangKeys = keyof typeof en

type LangMessages = { [k in LangKeys]: string }

export const zh: LangMessages = {
  convertToTemplate: '转换为模版',
  convertToTemplate_success: '转换成功',
  convertToTemplate_failure: '转换失败',
  templateCategory: '模版分类',
  templateRelease: '模版发布',
  templateManagement: '管理',
  released: '已上架',
  unreleased: '未上架',
  usage: '使用',
  edit: '编辑',
  release: '上架',
  unRelease: '下架',
  star: '点赞',
  usagePublicSuccess: '使用成功',
}

export function templateLangRender(key: LangKeys) {
  return () => templateLangT(key)
}

export function templateLangT(key: LangKeys) {
  return window['$t'](`template.${key}`)
}
