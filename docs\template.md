# 大屏新增模版功能

## 新增模型

以下数据结构，优先适应界面展示，数据库设计按需采纳

```ts
enum TemplateReleaseProgressEnum {
  /**
   * 未发布
   */
  UNPUBLISHED = 1,
  /**
   * 审核中
   */
  AUDITING = 2,
  /**
   * 审核通过
   */
  AUDITED = 3,
  /**
   * 审核不通过
   */
  AUDIT_FAILED = 4,
}

/**
 * 模板
 */
export interface Template {
  /**
   * JSON Schema
   */
  content: string
  /**
   * id
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 首页图片
   */
  indexImage: string
  /**
   * 备注
   */
  remarks: string
  /**
   * 所有者
   */
  owner: TemplateOwner
  /**
   * 来自一个模版/或者一个项目（大屏）
   */
  from: string
  /**
   * 星星
   */
  star: number
  /**
   * 使用次数
   */
  usage: number
  /**
   * 使用需要的积分数量
   */
  score: number
  /**
   * 标签 "数据运营,校园安全"
   */
  tags: string
  /**
   * 分类
   */
  category: TemplateCategory[]
  /**
   * 是否发布
   */
  release: boolean
  /**
   * 发布进度
   */
  releaseProgress: TemplateReleaseProgressEnum
  /**
   * 是否免费
   */
  free: boolean
  /**
   * 是否官方
   */
  official: boolean
  /**
   * 是否推荐
   */
  recommend: boolean
  /**
   * 创建时间
   */
  createdAt: string | number
  /**
   * 更新时间
   */
  updateAt: string | number
}

/**
 * 模板所有者
 */
export interface TemplateOwner {
  /**
   * id
   */
  id: 2
  /**
   * 昵称
   */
  nickname: '周润发'
  /**
   * 头像
   */
  avatar: 'https://goviewpro.goviewlink.com/avatar/2'
}

/**
 * 模板
 */
export interface TemplateCategory {
  /**
   * id
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 图标
   */
  icon: string
  /**
   * 颜色
   */
  color: string
  /**
   * 背景
   */
  background: string
  /**
   * 创建时间
   */
  createdAt: string | number
  /**
   * 更新时间
   */
  updateAt: string | number
}
```

## 新增用户接口

### 项目(大屏) 转换为模版

```ts
fetch('/project/convertToTemplate', {
  body: JSON.stringify({
    id: '4tx2zyelwtu000',
    template: {
      name: '智慧大屏数据平台',
      tags: '数据运营,校园安全',
      categoryId: '4tx2zyelwtu000',
      remarks: '这是一个智慧大屏数据平台'm,
    }
  })
})
```

### 查询我的模版

```ts
fetch('/template/my', {
  method: 'POST',
  body: JSON.stringify({
    page: 1,
    limit: 20,
    sortKey: 'updateAt', // createAt | updateAt
    sortValue: 'desc', // asc | desc
  }),
})
```

### 模版发布

把我的模版之一设置为公开供其他用户使用

```ts
fetch('/template/release', {
  method: 'POST',
  body: JSON.stringify({
    id: '4tx2zyelwtu000',
  }),
})
```

### 使用我的模版创建项目(大屏)

```ts
fetch('/template/createProject', {
  method: 'POST',
  body: JSON.stringify({
    id: '4tx2zyelwtu000',
  }),
})
```

### 删除我的模版

```ts
fetch('/template/my/delete', {
  method: 'POST',
  body: JSON.stringify({
    id: '4tx2zyelwtu000',
  }),
})
```

### 公开模版查询接口

```ts
fetch('/template/public/list', {
  method: 'POST',
  body: JSON.stringify({
    page: 1,
    limit: 40,
    sortKey: "usage",  // createAt | updateAt | usage | official | recommend | free | star
    sortValue: 'desc' // asc | desc
    categoryId: '4tx2zyelwtu000'
    searchType: 'all' // all | official | recommend | free
    keyword: '数据' // 搜索 tag 和 name
  })
})
```

### 使用公开模版

把公开模版复制一份给自己

```ts
fetch('/template/public/copy', {
  method: 'POST',
  body: JSON.stringify({
    id: '4tx2zyelwtu000',
    template: {
      name: '智慧大屏数据平台',
    },
  }),
})
```

### 模版分类查询接口

```ts
fetch('/template/category/list', {
  method: 'POST',
  body: JSON.stringify({
    page: 1,
    limit: 40
    sortKey: "sort",
    sortValue: 'desc' // asc | desc
  })
})
```

## 新增管理接口

### 模版管理四件套 (暂时不做)

#### 条件查询所有模版

```ts
fetch('/manage/template/list', {
  method: 'POST',
  body: JSON.stringify({
    page: 1,
    pageSize: 40,
    sortKey: "usage",  // createAt | updateAt | usage | official | recommend | free | star
    sortValue: 'desc' // asc | desc
    categoryId: '4tx2zyelwtu000'
    searchType: 'all' // all | official | recommend | free
    keyword: '数据' // 搜索 tag 和 name
    release: false // true | false 是否是公开模版
    releaseProgress: 1 // TemplateReleaseProgressEnum
  })
})
```

#### 下架模版

```ts
fetch('/manage/template/offline', {
  method: 'POST',
  body: JSON.stringify({
    id: '4tx2zyelwtu000',
  }),
})
```

### 模版分类管理四件套

#### 查询所有模版分类

```ts
fetch('/manage/template/category/list', {
  method: 'POST',
  body: JSON.stringify({
    page: 1,
    pageSize: 40,
    sortKey: 'sort',
    sortValue: 'desc', // asc | desc
  }),
})
```

#### 添加模版分类

```ts
fetch('/manage/template/category/add', {
  method: 'POST',
  body: JSON.stringify({
    name: '数据',
    color: '#fff',
    background: '#fff',
    sort: 1
    icon: "data:image/png;base64,iVBORw0KGgoAAAANSUhEXXXXXXX" // base64 | iconfiy icon
  })
})
```
